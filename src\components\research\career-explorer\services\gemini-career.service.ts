import { GoogleGenAI } from '@google/genai';
import { 
  CareerGenerationRequest, 
  CareerGenerationResponse, 
  CareerPath, 
  ResumeData 
} from '../types';
import { CAREER_PROMPTS } from '../constants';

/**
 * Google Gemini Career Service
 * Provides career path generation using Google's Gemini API
 */
export class GeminiCareerService {
  private ai: GoogleGenAI;
  private lastRequestTime: Date | null = null;
  private readonly delayBetweenRequests = 2000; // 2 seconds between requests

  constructor() {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;

    if (!apiKey) {
      throw new Error('Google Gemini API key not found. Please add VITE_GEMINI_API_KEY to your .env file.');
    }

    if (apiKey.length < 20) {
      throw new Error('Google Gemini API key appears to be invalid. Please check your VITE_GEMINI_API_KEY.');
    }

    this.ai = new GoogleGenAI({ apiKey });
    console.log('Gemini Career service initialized');
  }

  /**
   * Check if the service is properly configured
   */
  isConfigured(): boolean {
    const apiKey = import.meta.env.VITE_GEMINI_API_KEY;
    return Boolean(apiKey && apiKey.length > 20);
  }

  /**
   * Test the API connection
   */
  async testConnection(): Promise<{ success: boolean; error?: string }> {
    try {
      const response = await this.makeGeminiRequest(
        'Say "Hello, Career API test successful!" and nothing else.',
        'gemini-2.5-flash',
        { maxOutputTokens: 100, temperature: 0.1 }
      );

      if (response && response.trim().length > 0) {
        return { success: true };
      } else {
        return { success: false, error: 'Empty response from API' };
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error'
      };
    }
  }

  /**
   * Generate career paths using Gemini
   */
  async generateCareerPaths(request: CareerGenerationRequest): Promise<CareerGenerationResponse> {
    const startTime = Date.now();
    
    try {
      const prompt = this.buildInitialAnalysisPrompt(request.resumeText, request.additionalContext);
      
      const response = await this.makeGeminiRequest(
        prompt,
        request.model,
        {
          maxOutputTokens: 4096,
          temperature: 0.7
        }
      );

      // Parse the JSON response
      const careerData = this.parseCareerResponse(response);
      
      // Add unique IDs and additional properties
      const careers: CareerPath[] = careerData.map((career: any, index: number) => ({
        id: `career-${Date.now()}-${index}`,
        ...career,
        connectPosition: this.getConnectPosition(index)
      }));

      const processingTime = Date.now() - startTime;

      return {
        careers,
        analysis: 'Career analysis completed using Google Gemini',
        confidence: 0.9,
        processingTime
      };
    } catch (error) {
      console.error('Error generating career paths with Gemini:', error);
      throw new Error('Failed to generate career paths. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for a specific career path
   */
  async generateDetailedAnalysis(
    career: CareerPath, 
    resumeData: ResumeData, 
    additionalContext: string,
    model: string
  ): Promise<CareerPath> {
    try {
      const prompt = this.buildDetailedAnalysisPrompt(career, resumeData.rawText, additionalContext);
      
      const response = await this.makeGeminiRequest(
        prompt,
        model,
        {
          maxOutputTokens: 4096,
          temperature: 0.7
        }
      );

      const detailedData = this.parseDetailedResponse(response);
      
      // Convert roadmap format to match expected structure
      const roadmap = detailedData.roadmap?.map((step: any) => {
        const timeframe = Object.keys(step)[0];
        const description = Object.values(step)[0] as string;
        return {
          timeframe,
          description,
          tasks: [description],
          resources: []
        };
      }) || [];

      return {
        ...career,
        workRequired: detailedData.workRequired,
        aboutTheRole: detailedData.aboutTheRole,
        whyItsGoodFit: detailedData.whyItsGoodFit || detailedData.whyItsagoodfit, // Handle both formats
        roadmap,
        skills: detailedData.skills || [],
        certifications: detailedData.certifications || []
      };
    } catch (error) {
      console.error('Error generating detailed analysis with Gemini:', error);
      throw new Error('Failed to generate detailed career analysis. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for all career paths
   */
  async generateAllDetailedAnalyses(
    careers: CareerPath[], 
    resumeData: ResumeData, 
    additionalContext: string,
    model: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<CareerPath[]> {
    const detailedCareers: CareerPath[] = [];
    
    for (let i = 0; i < careers.length; i++) {
      try {
        const detailedCareer = await this.generateDetailedAnalysis(
          careers[i], 
          resumeData, 
          additionalContext, 
          model
        );
        detailedCareers.push(detailedCareer);
        
        if (onProgress) {
          onProgress(i + 1, careers.length);
        }
        
        // Add delay to avoid rate limiting
        if (i < careers.length - 1) {
          await this.enforceRateLimit();
        }
      } catch (error) {
        console.error(`Error generating detailed analysis for ${careers[i].jobTitle}:`, error);
        // Include the original career without detailed analysis
        detailedCareers.push(careers[i]);
      }
    }
    
    return detailedCareers;
  }

  /**
   * Make a request to Gemini with error handling
   */
  private async makeGeminiRequest(
    prompt: string,
    model: string,
    config: {
      maxOutputTokens: number;
      temperature: number;
    }
  ): Promise<string> {
    await this.enforceRateLimit();

    try {
      const requestConfig = {
        generationConfig: {
          maxOutputTokens: config.maxOutputTokens,
          temperature: config.temperature
        }
      };

      const contents = [{
        role: 'user' as const,
        parts: [{ text: prompt }],
      }];

      const response = await this.ai.models.generateContentStream({
        model,
        config: requestConfig,
        contents,
      });

      let fullText = '';
      for await (const chunk of response) {
        if (chunk.text) {
          fullText += chunk.text;
        }
      }

      if (!fullText || fullText.trim().length === 0) {
        throw new Error('Empty response from Gemini API');
      }

      return fullText;
    } catch (error) {
      console.error('Gemini API request failed:', error);
      throw new Error(`Gemini API error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Build the initial analysis prompt
   */
  private buildInitialAnalysisPrompt(resumeText: string, additionalContext: string): string {
    return `You are a professional career counselor and expert in career transitions. You ONLY respond with valid JSON.

Give me 6 career paths that the following user could transition into based on their resume and any additional context. Respond like this in JSON: {jobTitle: string, jobDescription: string, timeline: string, salary: string, difficulty: string}.

<example>
[
  {
    "jobTitle": "UX Designer",
    "jobDescription": "Creates user-centered design solutions to improve product usability and user experience.",
    "timeline": "3-6 months",
    "salary": "$85k - $110k",
    "difficulty": "Medium"
  },
  {
    "jobTitle": "Digital Marketing Specialist",
    "jobDescription": "Develops and implements online marketing campaigns to drive business growth.",
    "timeline": "2-4 months",
    "salary": "$50k - $70k",
    "difficulty": "Low"
  },
  {
    "jobTitle": "Software Engineer",
    "jobDescription": "Designs, develops, and tests software applications to meet business needs.",
    "timeline": "6-12 months",
    "salary": "$100k - $140k",
    "difficulty": "High"
  },
  {
    "jobTitle": "Business Analyst",
    "jobDescription": "Analyzes business needs and develops solutions to improve operations and processes.",
    "timeline": "3-6 months",
    "salary": "$65k - $90k",
    "difficulty": "Medium"
  },
  {
    "jobTitle": "Cybersecurity Specialist",
    "jobDescription": "Protects computer systems and networks from cyber threats by developing and implementing security protocols.",
    "timeline": "6-12 months",
    "salary": "$80k - $120k",
    "difficulty": "High"
  },
  {
    "jobTitle": "Data Analyst",
    "jobDescription": "Analyzes data to help organizations make informed business decisions.",
    "timeline": "4-8 months",
    "salary": "$60k - $85k",
    "difficulty": "Medium"
  }
]
</example>

<resume>
${resumeText}
</resume>

<additional_context>
${additionalContext || 'No additional context provided.'}
</additional_context>

ONLY respond with JSON, nothing else. Analyze the resume and context to suggest 6 diverse career paths that match the person's background and interests.`;
  }

  /**
   * Build the detailed analysis prompt
   */
  private buildDetailedAnalysisPrompt(career: CareerPath, resumeText: string, additionalContext: string): string {
    return `You are a helpful career expert that ONLY responds in JSON.

You are helping a person transition into the ${career.jobTitle} role in ${career.timeline}. Given the context about the person, return more information about the ${career.jobTitle} role in JSON as follows: {workRequired: string, aboutTheRole: string, whyItsagoodfit: array[], roadmap: [{string: string}, ...]

<example>
{"role": "DevOps Engineer",
"workRequired": "20-30 hrs/week",
"whyItsagoodfit": [
  "Leverages your extensive experience in software engineering and developer advocacy.",
  "Utilizes your skills in Python, JavaScript, Node.js, React, and cloud services like AWS.",
  "Aligns with your experience in building and maintaining large-scale applications and infrastructure.",
  "Allows you to continue working with cutting-edge technologies and practices."
],
"aboutTheRole": "As a DevOps Engineer, you will work closely with development, operations, and QA teams to streamline the software development lifecycle. Your responsibilities will include automating infrastructure provisioning, monitoring system performance, and ensuring security and compliance. The goal is to enhance the efficiency, reliability, and scalability of software deployments.",
"roadmap": [
  {"Weeks 1-2": "Learn the basics of DevOps tools and practices, including Docker and Kubernetes. Start with online courses or tutorials to build foundational knowledge."},
  {"Weeks 3-4": "Set up a local development environment with Docker and Kubernetes. Practice creating and managing containers and clusters."},
  {"Weeks 5-6": "Explore continuous integration and continuous delivery (CI/CD) concepts. Implement a simple CI/CD pipeline using tools like Jenkins or GitLab CI."},
  {"Weeks 7-8": "Familiarize yourself with configuration management tools like Ansible or Terraform. Practice writing scripts to automate infrastructure provisioning."},
  {"Weeks 9-10": "Obtain a relevant certification such as AWS Certified DevOps Engineer or Google Cloud Professional DevOps Engineer to validate your skills."},
  {"Weeks 11-12": "Set up monitoring and logging solutions using tools like Prometheus, Grafana, and ELK stack. Learn to monitor system performance and troubleshoot issues."},
  {"Weeks 13-14": "Optimize your CI/CD pipelines for efficiency, scalability, and reliability. Implement advanced deployment strategies such as blue-green deployments or canary releases."},
  {"Weeks 15-16": "Collaborate with development and operations teams on real projects to apply your skills in a practical setting. Seek feedback and continuously improve your processes."}
]}
</example>

<context>
${resumeText}
${additionalContext}
</context>

ONLY respond with JSON, nothing else.`;
  }

  /**
   * Parse career response from Gemini
   */
  private parseCareerResponse(response: string): any[] {
    try {
      // Clean the response to extract JSON
      let cleanedResponse = response.trim();
      
      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/, '');
      
      // Try to parse the JSON
      const parsed = JSON.parse(cleanedResponse);
      
      if (Array.isArray(parsed)) {
        return parsed;
      } else {
        throw new Error('Response is not an array');
      }
    } catch (error) {
      console.error('Failed to parse career response:', error);
      console.error('Raw response:', response);
      throw new Error('Failed to parse career data from AI response');
    }
  }

  /**
   * Parse detailed response from Gemini
   */
  private parseDetailedResponse(response: string): any {
    try {
      // Clean the response to extract JSON
      let cleanedResponse = response.trim();
      
      // Remove markdown code blocks if present
      cleanedResponse = cleanedResponse.replace(/```json\s*/, '').replace(/```\s*$/, '');
      cleanedResponse = cleanedResponse.replace(/```\s*/, '');
      
      // Try to parse the JSON
      return JSON.parse(cleanedResponse);
    } catch (error) {
      console.error('Failed to parse detailed response:', error);
      console.error('Raw response:', response);
      throw new Error('Failed to parse detailed career data from AI response');
    }
  }

  /**
   * Determine connection position for visualization
   */
  private getConnectPosition(index: number): 'top' | 'bottom' {
    return index % 2 === 0 ? 'bottom' : 'top';
  }

  /**
   * Enforce rate limiting between requests
   */
  private async enforceRateLimit(): Promise<void> {
    if (this.lastRequestTime) {
      const timeSinceLastRequest = Date.now() - this.lastRequestTime.getTime();
      const remainingDelay = this.delayBetweenRequests - timeSinceLastRequest;

      if (remainingDelay > 0) {
        console.log(`Rate limiting: waiting ${remainingDelay}ms before next request`);
        await this.sleep(remainingDelay);
      }
    }

    this.lastRequestTime = new Date();
  }

  /**
   * Sleep utility for delays
   */
  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// Export singleton instance
export const geminiCareerService = new GeminiCareerService();
export default geminiCareerService;
