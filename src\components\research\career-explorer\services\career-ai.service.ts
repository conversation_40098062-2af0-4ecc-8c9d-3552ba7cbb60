import {
  CareerGenerationRequest,
  CareerGenerationResponse,
  CareerPath,
  ResumeData
} from '../types';
import { CAREER_PROMPTS, AI_MODELS } from '../constants';
import { geminiCareerService } from './gemini-career.service';

class CareerAIService {
  private baseUrl = '/api/openrouter';

  /**
   * Generate initial career paths based on resume and context
   */
  async generateCareerPaths(request: CareerGenerationRequest): Promise<CareerGenerationResponse> {
    // Determine which service to use based on the model
    const modelConfig = AI_MODELS.find(m => m.id === request.model);

    if (modelConfig?.provider === 'gemini') {
      // Use Gemini service for Gemini models
      return await geminiCareerService.generateCareerPaths(request);
    } else {
      // Use OpenRouter service for other models
      return await this.generateCareerPathsOpenRouter(request);
    }
  }

  /**
   * Generate career paths using OpenRouter API
   */
  private async generateCareerPathsOpenRouter(request: CareerGenerationRequest): Promise<CareerGenerationResponse> {
    const startTime = Date.now();

    try {
      const prompt = this.buildInitialAnalysisPrompt(request.resumeText, request.additionalContext);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: request.model,
          messages: [
            {
              role: 'system',
              content: 'You are a professional career counselor and expert in career transitions. You ONLY respond with valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 4096
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const data = await response.json();
      const careerData = JSON.parse(data.choices[0].message.content);

      // Add unique IDs and additional properties
      const careers: CareerPath[] = careerData.map((career: any, index: number) => ({
        id: `career-${Date.now()}-${index}`,
        ...career,
        connectPosition: this.getConnectPosition(index)
      }));

      const processingTime = Date.now() - startTime;

      return {
        careers,
        analysis: 'Initial career analysis completed',
        confidence: 0.85,
        processingTime
      };
    } catch (error) {
      console.error('Error generating career paths:', error);
      throw new Error('Failed to generate career paths. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for a specific career path
   */
  async generateDetailedAnalysis(
    career: CareerPath,
    resumeData: ResumeData,
    additionalContext: string,
    model: string
  ): Promise<CareerPath> {
    // Determine which service to use based on the model
    const modelConfig = AI_MODELS.find(m => m.id === model);

    if (modelConfig?.provider === 'gemini') {
      // Use Gemini service for Gemini models
      return await geminiCareerService.generateDetailedAnalysis(career, resumeData, additionalContext, model);
    } else {
      // Use OpenRouter service for other models
      return await this.generateDetailedAnalysisOpenRouter(career, resumeData, additionalContext, model);
    }
  }

  /**
   * Generate detailed analysis using OpenRouter API
   */
  private async generateDetailedAnalysisOpenRouter(
    career: CareerPath,
    resumeData: ResumeData,
    additionalContext: string,
    model: string
  ): Promise<CareerPath> {
    try {
      const prompt = this.buildDetailedAnalysisPrompt(career, resumeData.rawText, additionalContext);

      const response = await fetch(this.baseUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model,
          messages: [
            {
              role: 'system',
              content: 'You are a career expert providing detailed transition guidance. You ONLY respond with valid JSON.'
            },
            {
              role: 'user',
              content: prompt
            }
          ],
          temperature: 0.7,
          max_tokens: 4096
        })
      });

      if (!response.ok) {
        throw new Error(`API request failed: ${response.statusText}`);
      }

      const data = await response.json();
      const detailedData = JSON.parse(data.choices[0].message.content);

      // Convert roadmap format
      const roadmap = detailedData.roadmap?.map((step: any) => {
        const timeframe = Object.keys(step)[0];
        const description = Object.values(step)[0] as string;
        return {
          timeframe,
          description,
          tasks: [description], // Split into tasks if needed
          resources: []
        };
      }) || [];

      return {
        ...career,
        workRequired: detailedData.workRequired,
        aboutTheRole: detailedData.aboutTheRole,
        whyItsGoodFit: detailedData.whyItsGoodFit,
        roadmap,
        skills: detailedData.skills || [],
        certifications: detailedData.certifications || []
      };
    } catch (error) {
      console.error('Error generating detailed analysis:', error);
      throw new Error('Failed to generate detailed career analysis. Please try again.');
    }
  }

  /**
   * Generate detailed analysis for all career paths
   */
  async generateAllDetailedAnalyses(
    careers: CareerPath[],
    resumeData: ResumeData,
    additionalContext: string,
    model: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<CareerPath[]> {
    // Determine which service to use based on the model
    const modelConfig = AI_MODELS.find(m => m.id === model);

    if (modelConfig?.provider === 'gemini') {
      // Use Gemini service for Gemini models
      return await geminiCareerService.generateAllDetailedAnalyses(
        careers,
        resumeData,
        additionalContext,
        model,
        onProgress
      );
    } else {
      // Use OpenRouter service for other models
      return await this.generateAllDetailedAnalysesOpenRouter(
        careers,
        resumeData,
        additionalContext,
        model,
        onProgress
      );
    }
  }

  /**
   * Generate detailed analysis for all career paths using OpenRouter
   */
  private async generateAllDetailedAnalysesOpenRouter(
    careers: CareerPath[],
    resumeData: ResumeData,
    additionalContext: string,
    model: string,
    onProgress?: (completed: number, total: number) => void
  ): Promise<CareerPath[]> {
    const detailedCareers: CareerPath[] = [];

    for (let i = 0; i < careers.length; i++) {
      try {
        const detailedCareer = await this.generateDetailedAnalysisOpenRouter(
          careers[i],
          resumeData,
          additionalContext,
          model
        );
        detailedCareers.push(detailedCareer);

        if (onProgress) {
          onProgress(i + 1, careers.length);
        }

        // Add small delay to avoid rate limiting
        if (i < careers.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
        }
      } catch (error) {
        console.error(`Error generating detailed analysis for ${careers[i].jobTitle}:`, error);
        // Include the original career without detailed analysis
        detailedCareers.push(careers[i]);
      }
    }

    return detailedCareers;
  }

  /**
   * Build the initial analysis prompt
   */
  private buildInitialAnalysisPrompt(resumeText: string, additionalContext: string): string {
    return `${CAREER_PROMPTS.initialAnalysis}

<resume>
${resumeText}
</resume>

<additional_context>
${additionalContext || 'No additional context provided.'}
</additional_context>

Analyze the resume and context to suggest 6 diverse career paths that match the person's background and interests. Consider their existing skills, experience level, and potential for growth.`;
  }

  /**
   * Build the detailed analysis prompt
   */
  private buildDetailedAnalysisPrompt(career: CareerPath, resumeText: string, additionalContext: string): string {
    const prompt = CAREER_PROMPTS.detailedAnalysis
      .replace('{jobTitle}', career.jobTitle);

    return `${prompt}

<career_info>
Job Title: ${career.jobTitle}
Description: ${career.jobDescription}
Timeline: ${career.timeline}
Difficulty: ${career.difficulty}
</career_info>

<resume>
${resumeText}
</resume>

<additional_context>
${additionalContext || 'No additional context provided.'}
</additional_context>

Provide detailed guidance for transitioning into this role, including specific steps, required skills, and why it's a good fit based on their background.`;
  }

  /**
   * Determine connection position for visualization
   */
  private getConnectPosition(index: number): 'top' | 'bottom' {
    // Alternate between top and bottom for visual balance
    return index % 2 === 0 ? 'bottom' : 'top';
  }

  /**
   * Validate career data structure
   */
  private validateCareerData(data: any): boolean {
    const requiredFields = ['jobTitle', 'jobDescription', 'timeline', 'salary', 'difficulty'];
    return requiredFields.every(field => data.hasOwnProperty(field));
  }

  /**
   * Parse resume text and extract key information
   */
  parseResumeText(text: string): ResumeData['extractedInfo'] {
    // Basic extraction logic - can be enhanced with more sophisticated parsing
    const lines = text.split('\n').map(line => line.trim()).filter(line => line.length > 0);
    
    const skills: string[] = [];
    const experience: string[] = [];
    const education: string[] = [];
    const certifications: string[] = [];

    // Simple keyword-based extraction
    lines.forEach(line => {
      const lowerLine = line.toLowerCase();
      
      if (lowerLine.includes('skill') || lowerLine.includes('technology') || lowerLine.includes('programming')) {
        skills.push(line);
      } else if (lowerLine.includes('experience') || lowerLine.includes('worked') || lowerLine.includes('developed')) {
        experience.push(line);
      } else if (lowerLine.includes('education') || lowerLine.includes('degree') || lowerLine.includes('university')) {
        education.push(line);
      } else if (lowerLine.includes('certification') || lowerLine.includes('certified') || lowerLine.includes('license')) {
        certifications.push(line);
      }
    });

    return {
      skills: skills.slice(0, 10), // Limit to top 10
      experience: experience.slice(0, 5),
      education: education.slice(0, 3),
      certifications: certifications.slice(0, 5)
    };
  }
}

export const careerAIService = new CareerAIService();
